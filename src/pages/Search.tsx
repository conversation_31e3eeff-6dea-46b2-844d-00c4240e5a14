import { useEffect } from "react";
import { SearchInterface } from "@/components/search/SearchInterface";
import { initializeDatabase } from "@/integrations/supabase/client";

const Search = () => {
  useEffect(() => {
    // Initialize database and assign orphaned candidates
    initializeDatabase();
  }, []);

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="min-w-0">
        <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6">Search</h1>
        <SearchInterface />
      </div>
    </div>
  );
};

export default Search;
