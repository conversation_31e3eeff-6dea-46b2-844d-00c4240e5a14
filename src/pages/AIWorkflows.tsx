import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AIWorkflowCanvas } from "@/components/ai/AIWorkflowCanvas";
import { WorkflowManager } from "@/components/ai/workflow/WorkflowManager";
import { WorkflowTemplates } from "@/components/ai/workflow/WorkflowTemplates";
import { WorkflowAnalytics } from "@/components/ai/workflow/WorkflowAnalytics";
import { WorkflowIntegrations } from "@/components/ai/workflow/WorkflowIntegrations";
import { WorkflowSuggestions } from "@/components/ai/workflow/WorkflowSuggestions";
import { useLocation, useNavigate } from "react-router-dom";
import { useWorkflowConfigurations } from "@/hooks/useWorkflowConfigurations";

const AIWorkflows = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { data: workflows } = useWorkflowConfigurations();

  // Get tab from URL query parameter
  const queryParams = new URLSearchParams(location.search);
  const tabParam = queryParams.get("tab");
  const editParam = queryParams.get("edit");

  const [activeTab, setActiveTab] = useState(tabParam || "canvas");
  const [editWorkflowId, setEditWorkflowId] = useState<string | null>(
    editParam,
  );

  // Update URL when tab changes
  useEffect(() => {
    const params = new URLSearchParams();
    params.set("tab", activeTab);
    if (editWorkflowId) {
      params.set("edit", editWorkflowId);
    }
    navigate(`/ai-workflows?${params.toString()}`, { replace: true });
  }, [activeTab, editWorkflowId, navigate]);

  // Handle edit workflow parameter
  useEffect(() => {
    if (editParam) {
      setActiveTab("canvas");
      setEditWorkflowId(editParam);
    }
  }, [editParam]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === "manage") {
      // Clear edit parameter when switching to manage tab
      setEditWorkflowId(null);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">AI Workflows</h1>

      <Tabs
        defaultValue="canvas"
        value={activeTab}
        onValueChange={handleTabChange}
      >
        <TabsList className="flex flex-wrap gap-x-4 gap-y-2">
          <TabsTrigger value="canvas">Workflow Editor</TabsTrigger>
          <TabsTrigger value="manage">Manage Workflows</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="suggestions">AI Suggestions</TabsTrigger>
        </TabsList>

        <TabsContent value="canvas" className="mt-6">
          <AIWorkflowCanvas editWorkflowId={editWorkflowId} />
        </TabsContent>

        <TabsContent value="manage" className="mt-6">
          <WorkflowManager />
        </TabsContent>

        <TabsContent value="templates" className="mt-6">
          <WorkflowTemplates />
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <WorkflowAnalytics />
        </TabsContent>

        <TabsContent value="integrations" className="mt-6">
          <WorkflowIntegrations />
        </TabsContent>

        <TabsContent value="suggestions" className="mt-6">
          <WorkflowSuggestions />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIWorkflows;
