import React, { useContext, useState, useEffect } from "react";
import { CalendarView } from "@/components/calendar/CalendarView";
import { UpcomingEvents } from "@/components/calendar/UpcomingEvents";
import { TaskCalendarWidget } from "@/components/tasks/TaskCalendarWidget";
import { LocalEventsContext } from "@/components/layout/AppLayout";
import { Menu, X, ChevronRight, ChevronLeft } from "lucide-react";

const Calendar = () => {
  const { localEvents, setLocalEvents } = useContext(LocalEventsContext);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date(),
  );
  // Initialize drawer state: default to closed unless explicitly opened before
  const [isDrawerOpen, setIsDrawerOpen] = useState(() => {
    // Check localStorage first - if user has explicitly set a preference, respect it
    const savedState = localStorage.getItem("isDrawerOpen");
    if (savedState !== null) {
      return savedState === "true";
    }
    // Default to closed
    return false;
  });

  // Function to toggle drawer state
  const toggleDrawer = () => {
    setIsDrawerOpen((prev) => {
      const newState = !prev;
      localStorage.setItem("isDrawerOpen", String(newState));
      return newState;
    });
  };

  // Handle escape key to close drawer on mobile
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isDrawerOpen && window.innerWidth < 1024) {
        toggleDrawer();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isDrawerOpen]);

  return (
    <div className="max-w-screen-2xl mx-auto space-y-4 sm:space-y-6 p-4 sm:p-0">
      <h1 className="text-3xl font-bold">Calendar</h1>

      <div className="flex flex-col lg:flex-row gap-4 sm:gap-6">
        {/* Main calendar area - full width on large screens */}
        <div className="flex-1 min-w-0 relative" id="calendar-container">
          <CalendarView
            onEventsChange={setLocalEvents}
            onDateSelect={(date) => setSelectedDate(date || undefined)}
          />

          {/* Desktop toggle button */}
          <button
            className="hidden lg:flex absolute top-4 right-4 items-center gap-1 px-3 py-2 bg-white border rounded-lg shadow-sm hover:bg-gray-50 transition-colors z-10"
            onClick={toggleDrawer}
            aria-label={
              isDrawerOpen ? "Hide tasks sidebar" : "Show tasks sidebar"
            }
          >
            {isDrawerOpen ? (
              <>
                <span className="text-sm font-medium">Hide Tasks</span>
                <ChevronRight className="h-4 w-4" />
              </>
            ) : (
              <>
                <ChevronLeft className="h-4 w-4" />
                <span className="text-sm font-medium">Show Tasks</span>
              </>
            )}
          </button>
          {/* Toggle button for drawer */}
          <button
            className="fixed bottom-4 right-4 lg:hidden bg-primary p-3 rounded-full shadow-lg z-40 hover:bg-primary/90 transition-colors"
            onClick={() => {
              toggleDrawer();
              // Restore focus to calendar for accessibility
              if (isDrawerOpen) {
                // When closing the drawer, return focus to main content
                const calendarContainer =
                  document.getElementById("calendar-container");
                if (calendarContainer) {
                  calendarContainer.setAttribute("tabIndex", "-1");
                  calendarContainer.focus();
                  // Remove tabIndex after focus
                  setTimeout(
                    () => calendarContainer.removeAttribute("tabIndex"),
                    100,
                  );
                }
              }
            }}
          >
            {isDrawerOpen ? (
              <X className="h-5 w-5 text-white" />
            ) : (
              <Menu className="h-5 w-5 text-white" />
            )}
          </button>
        </div>

        {/* Mobile overlay */}
        {isDrawerOpen && (
          <div
            className="fixed inset-0 bg-black/20 z-20 lg:hidden"
            onClick={toggleDrawer}
            aria-hidden="true"
          />
        )}

        {/* Task Drawer container */}
        <aside
          className={`
            fixed lg:relative top-0 right-0 h-full lg:h-auto
            w-full sm:w-96 lg:w-80 xl:w-96
            bg-white shadow-xl lg:shadow-none
            transform transition-transform duration-300 ease-in-out
            ${isDrawerOpen ? "translate-x-0" : "translate-x-full"}
            lg:transform-none lg:transition-none
            ${!isDrawerOpen && "lg:hidden"}
            overflow-y-auto z-30
          `}
          aria-label="Task sidebar"
          aria-hidden={!isDrawerOpen}
        >
          {/* Mobile close button */}
          <div className="lg:hidden sticky top-0 bg-white border-b p-4 flex justify-between items-center">
            <h2 className="text-lg font-semibold">Tasks</h2>
            <button
              onClick={toggleDrawer}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Close tasks sidebar"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <TaskCalendarWidget
            selectedDate={selectedDate}
            showOverdue={true}
            showDueToday={true}
            showSelectedDate={true}
          />
        </aside>
      </div>
    </div>
  );
};

export default Calendar;
