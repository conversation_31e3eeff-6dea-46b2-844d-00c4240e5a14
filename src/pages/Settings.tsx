import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs";
import { SettingsNotifications } from "@/components/settings/SettingsNotifications";
import { SettingsAI } from "@/components/settings/SettingsAI";
import { SettingsProfile } from "@/components/settings/SettingsProfile";
import { MessageTemplateManager } from "@/components/message/MessageTemplateManager";
import { SettingsSecurity } from "@/components/settings/SettingsSecurity";
import { SettingsMobile } from "@/components/settings/SettingsMobile";
import { SettingsInternationalization } from "@/components/settings/SettingsInternationalization";
import { SettingsPerformance } from "@/components/settings/SettingsPerformance";
import { PerformanceMonitor } from "@/components/performance/PerformanceMonitor";
import { SystemHealth } from "@/components/dashboard/SystemHealth";

const Settings = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="w-full flex flex-wrap gap-1 h-auto p-1">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="mobile">Mobile</TabsTrigger>
          <TabsTrigger value="i18n">Language</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="ai">AI Settings</TabsTrigger>
          <TabsTrigger value="templates">Message Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <SettingsProfile />
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <SettingsSecurity />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <SettingsNotifications />
        </TabsContent>

        <TabsContent value="mobile" className="space-y-6">
          <SettingsMobile />
        </TabsContent>

        <TabsContent value="i18n" className="space-y-6">
          <SettingsInternationalization />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-12">
            <div className="lg:col-span-8">
              <SettingsPerformance />
            </div>
            <div className="lg:col-span-4 space-y-6">
              <PerformanceMonitor />
              <SystemHealth />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="ai" className="space-y-6">
          <SettingsAI />
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <MessageTemplateManager />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
