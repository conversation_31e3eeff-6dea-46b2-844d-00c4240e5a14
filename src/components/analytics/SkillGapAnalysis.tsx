import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import {
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  Brain,
  TrendingUp,
  AlertCircle,
  Star,
  Sparkles,
  Loader2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Progress } from "@/components/ui/progress";
import { useAnalyticsSkills } from "@/hooks/useAnalyticsSkills";
import { Skeleton } from "@/components/ui/skeleton";
import { useState } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";

export const SkillGapAnalysis = ({
  expanded = false,
}: {
  expanded?: boolean;
}) => {
  const { data: skillsData = [], isLoading } = useAnalyticsSkills();
  const { toast } = useToast();
  const [aiAnalysis, setAiAnalysis] = useState<string>("");
  const [isGeneratingAnalysis, setIsGeneratingAnalysis] = useState(false);

  // The data is already transformed in the hook, so we can use it directly
  const skillGapAnalysis = skillsData;

  if (isLoading) {
    return (
      <div className={expanded ? "h-full overflow-y-auto" : ""}>
        <Card className={expanded ? "h-full border-0 shadow-none" : ""}>
          <CardHeader className="flex flex-row items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent
            className={`max-h-[75vh] ${expanded ? "overflow-y-auto" : "h-[400px]"}`}
          >
            <Skeleton className="h-full w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }
  const getGapSeverity = (gap: number) => {
    if (gap <= 5) return { color: "bg-green-500", text: "Minor Gap" };
    if (gap <= 10) return { color: "bg-yellow-500", text: "Moderate Gap" };
    return { color: "bg-red-500", text: "Significant Gap" };
  };

  const calculateSuccessProbability = () => {
    const totalImpact = skillGapAnalysis.reduce(
      (sum, skill) => sum + skill.successImpact,
      0,
    );
    return Math.round((totalImpact / (skillGapAnalysis.length * 100)) * 100);
  };

  const generateAIAnalysis = async () => {
    if (!skillGapAnalysis.length || isGeneratingAnalysis) return;

    setIsGeneratingAnalysis(true);
    try {
      const skillData = skillGapAnalysis
        .map(
          (skill) =>
            `${skill.skill}: Required ${skill.required}%, Current ${skill.current}%, Gap ${skill.gap}%`,
        )
        .join("\n");

      const prompt = `
Analyze the following skill gap data for a recruitment team:

${skillData}

Provide a comprehensive analysis including:
1. Overall skill gap assessment and trends
2. Priority skills that need immediate attention
3. Strategic recommendations for addressing gaps
4. Hiring and training suggestions
5. Market insights on skill availability

Keep the response actionable and focused on practical solutions for recruitment teams.
`;

      const systemPrompt =
        "You are an expert talent acquisition strategist specializing in skill gap analysis. Provide strategic insights that help recruitment teams make informed decisions about hiring priorities and skill development.";

      const analysis = await generateText(prompt, systemPrompt);
      setAiAnalysis(analysis);

      toast({
        title: "AI Analysis Complete",
        description: "Skill gap analysis and recommendations are ready.",
      });
    } catch (error) {
      console.error("Error generating AI analysis:", error);
      toast({
        title: "Analysis Unavailable",
        description: "Unable to generate AI analysis at the moment.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingAnalysis(false);
    }
  };

  const successProbability = calculateSuccessProbability();

  return (
    <div className={expanded ? "h-full overflow-y-auto" : ""}>
      <Card className={expanded ? "h-full border-0 shadow-none" : ""}>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-4">
            <CardTitle id="skill-gap-analysis-title" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Skill Gap Analysis
            </CardTitle>
            <HoverCard>
              <HoverCardTrigger asChild>
                <AlertCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </HoverCardTrigger>
              <HoverCardContent className="w-80">
                <div className="space-y-2">
                  <h4 className="font-medium">AI-Powered Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    This analysis compares skills against requirements and
                    predicts success probability based on skill matches and
                    gaps.
                  </p>
                </div>
              </HoverCardContent>
            </HoverCard>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">
                Success Probability: {successProbability}%
              </span>
            </div>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent
          className={`max-h-[75vh] ${expanded ? "overflow-y-auto" : "h-[400px]"}`}
        >
          <div
            className={`grid grid-cols-1 md:grid-cols-2 gap-6 ${expanded ? "min-h-[500px]" : "h-full"}`}
          >
            <ResponsiveContainer width="100%" height={expanded ? 400 : "100%"}>
              <RadarChart data={skillGapAnalysis}>
                <PolarGrid />
                <PolarAngleAxis dataKey="skill" />
                <PolarRadiusAxis />
                <Radar
                  name="Current Level"
                  dataKey="current"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.6}
                />
                <Radar
                  name="Required Level"
                  dataKey="required"
                  stroke="#10b981"
                  fill="#10b981"
                  fillOpacity={0.6}
                />
                <Tooltip />
              </RadarChart>
            </ResponsiveContainer>
            <div className="space-y-4 overflow-auto max-h-full pr-2">
              {skillGapAnalysis.map((skill) => {
                const severity = getGapSeverity(skill.gap);
                return (
                  <Card key={skill.skill} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{skill.skill}</h4>
                      <Badge className={severity.color + " text-white"}>
                        {severity.text}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>Current: {skill.current}%</span>
                        <span>Required: {skill.required}%</span>
                      </div>
                      <Progress
                        value={(skill.current / skill.required) * 100}
                      />
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">
                          Success Impact
                        </span>
                        <span className="font-medium">
                          {skill.successImpact}%
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        {skill.recommendation}
                      </p>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* AI Analysis Section */}
          {expanded && (
            <div className="mt-6 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-700 flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  AI Strategic Analysis
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateAIAnalysis}
                  disabled={isGeneratingAnalysis}
                >
                  {isGeneratingAnalysis ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Analysis
                    </>
                  )}
                </Button>
              </div>

              {aiAnalysis && (
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4">
                  <div className="text-sm text-gray-700 whitespace-pre-wrap">
                    {aiAnalysis}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
