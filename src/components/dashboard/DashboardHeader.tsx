import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  Sheet<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Calendar, Download, Filter } from "lucide-react";
import { DashboardFilters } from "./DashboardFilters";
import { useDashboardFilters } from "@/contexts/DashboardFiltersContext";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useCandidates } from "@/hooks/useCandidates";
import { useJobs } from "@/hooks/useJobs";
import { useEvents } from "@/hooks/useEvents";
import { useProfile } from "@/hooks/useProfiles";

export function DashboardHeader() {
  const { componentVisibility } = useDashboardFilters();
  const { toast } = useToast();
  const { data: profile } = useProfile();
  const [selectedPeriod, setSelectedPeriod] = useState("today");
  const [isExporting, setIsExporting] = useState(false);

  // Get data for export
  const { data: candidates = [] } = useCandidates();
  const { data: jobs = [] } = useJobs();
  const { data: events = [] } = useEvents();

  // Count visible components for display
  const visibleCount =
    Object.values(componentVisibility).filter(Boolean).length;
  const totalCount = Object.keys(componentVisibility).length;

  // Filter data based on selected period
  const getFilteredData = () => {
    const now = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case "today":
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case "week":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case "quarter":
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        startDate = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case "year":
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(0); // All time
    }

    return {
      candidates: candidates.filter((c) => new Date(c.createdAt) >= startDate),
      jobs: jobs.filter((j) => new Date(j.created_at) >= startDate),
      events: events.filter((e) => new Date(e.start_time) >= startDate),
    };
  };

  // Export dashboard data as CSV
  const handleExport = async () => {
    setIsExporting(true);

    try {
      const filteredData = getFilteredData();

      // Create CSV content
      const csvContent = [
        // Header
        "Dashboard Export - " + new Date().toLocaleDateString(),
        "",
        "Period: " + selectedPeriod,
        "Generated: " + new Date().toLocaleString(),
        "",
        "SUMMARY",
        `Total Candidates,${filteredData.candidates.length}`,
        `Active Jobs,${filteredData.jobs.filter((j) => j.is_active).length}`,
        `Total Events,${filteredData.events.length}`,
        "",
        "CANDIDATES",
        "Name,Email,Role,Location,Experience,Relationship Score,Created Date",
        ...filteredData.candidates.map(
          (c) =>
            `"${c.name}","${c.email}","${c.role}","${c.location || ""}","${c.experience || ""}",${c.relationshipScore || 0},"${new Date(c.createdAt).toLocaleDateString()}"`,
        ),
        "",
        "JOBS",
        "Title,Department,Location,Type,Status,Applicants,Created Date",
        ...filteredData.jobs.map(
          (j) =>
            `"${j.title}","${j.department_name || j.department || ""}","${j.location_name || j.location}","${j.job_type_name || j.job_type || ""}","${j.is_active ? "Active" : "Inactive"}",${j.applicant_count},"${new Date(j.created_at).toLocaleDateString()}"`,
        ),
        "",
        "EVENTS",
        "Title,Type,Category,Priority,Start Time,Location",
        ...filteredData.events.map(
          (e) =>
            `"${e.title}","${e.event_type_name || e.event_type || ""}","${e.category}","${e.priority_name || e.priority || ""}","${new Date(e.start_time).toLocaleString()}","${e.location_name || e.location || ""}"`,
        ),
      ].join("\n");

      // Create and download file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `dashboard-export-${selectedPeriod}-${new Date().toISOString().split("T")[0]}.csv`,
      );
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Export Successful",
        description: `Dashboard data exported for ${selectedPeriod} period.`,
      });
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export Failed",
        description: "Unable to export dashboard data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div className="min-w-0">
        <h1 className="text-2xl sm:text-3xl font-bold">Dashboard</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Welcome back{profile?.first_name ? `, ${profile.first_name}` : ""}! Here's your recruitment overview.
          {visibleCount < totalCount && (
            <span className="ml-2 text-xs bg-muted px-2 py-1 rounded">
              {visibleCount}/{totalCount} components shown
            </span>
          )}
        </p>
      </div>
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full md:w-auto">
        <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <Calendar className="mr-1 sm:mr-2 h-4 w-4" />
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="quarter">This Quarter</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex gap-2">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="flex-1 sm:flex-initial">
                <Filter className="mr-1 sm:mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Filters</span>
                {visibleCount < totalCount && (
                  <span className="ml-1 sm:ml-2 bg-primary text-primary-foreground text-xs px-1.5 py-0.5 rounded-full">
                    {visibleCount}
                  </span>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Dashboard Filters</SheetTitle>
              </SheetHeader>
              <DashboardFilters />
            </SheetContent>
          </Sheet>
          <Button
            variant="outline"
            className="flex-1 sm:flex-initial"
            onClick={handleExport}
            disabled={isExporting}
          >
            <Download className="mr-1 sm:mr-2 h-4 w-4" />
            <span className="hidden sm:inline">
              {isExporting ? "Exporting..." : "Export"}
            </span>
          </Button>
        </div>
      </div>
    </div>
  );
}
