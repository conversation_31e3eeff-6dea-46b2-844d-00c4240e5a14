import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { QuickActions } from "./overview/QuickActions";
import { MatchedJobsList } from "./overview/MatchedJobsList";
import { RelationshipScoreDialog } from "./overview/RelationshipScoreDialog";
import { CandidateType } from "@/types/candidate";
import {
  MapPin,
  Briefcase,
  GraduationCap,
  Globe,
  Github,
  Linkedin,
  Twitter,
  Phone,
  Mail,
} from "lucide-react";
import { useState } from "react";

interface CandidateOverviewProps {
  candidate: CandidateType;
}

export function CandidateOverview({ candidate }: CandidateOverviewProps) {
  const [showRelationshipDialog, setShowRelationshipDialog] = useState(false);

  const socialLinks = [
    {
      icon: Gith<PERSON>,
      url: candidate.socialLinks?.github,
      label: "GitHub",
      show: candidate.socialLinks?.github,
    },
    {
      icon: Linkedin,
      url: candidate.socialLinks?.linkedin,
      label: "LinkedIn",
      show: candidate.socialLinks?.linkedin,
    },
    {
      icon: Twitter,
      url: candidate.socialLinks?.twitter,
      label: "Twitter",
      show: candidate.socialLinks?.twitter,
    },
  ].filter((link) => link.show);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Profile Info */}
      <div className="lg:col-span-2 space-y-6">
        {/* Basic Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={candidate.avatar} alt={candidate.name} />
                <AvatarFallback>
                  {candidate.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-bold">{candidate.name}</h2>
                <p className="text-muted-foreground">{candidate.role}</p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Contact Info */}
            <div className="flex flex-wrap gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{candidate.email}</span>
              </div>
              {candidate.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{candidate.phone}</span>
                </div>
              )}
              {candidate.location && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{candidate.location}</span>
                </div>
              )}
            </div>

            {/* Professional Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {candidate.experience && (
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{candidate.experience}</span>
                </div>
              )}
              {candidate.industry && (
                <div className="flex items-center gap-2">
                  <GraduationCap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{candidate.industry}</span>
                </div>
              )}
              {candidate.remotePreference && (
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{candidate.remotePreference}</span>
                </div>
              )}
              {candidate.visaStatus && (
                <div className="flex items-center gap-2">
                  <span className="text-sm">Visa: {candidate.visaStatus}</span>
                </div>
              )}
            </div>

            {/* Social Links */}
            {socialLinks.length > 0 && (
              <div className="flex gap-2">
                {socialLinks.map(({ icon: Icon, url, label }) => (
                  <Button key={label} variant="outline" size="sm" asChild>
                    <a href={url} target="_blank" rel="noopener noreferrer">
                      <Icon className="h-4 w-4 mr-2" />
                      {label}
                    </a>
                  </Button>
                ))}
              </div>
            )}

            {/* Tags */}
            {candidate.normalized_tags && candidate.normalized_tags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {candidate.normalized_tags.map((tag) => (
                  <Badge 
                    key={tag.id} 
                    variant="secondary"
                    style={{
                      backgroundColor: tag.color ? `${tag.color}20` : undefined,
                      borderColor: tag.color || undefined,
                      color: tag.color || undefined,
                    }}
                  >
                    {tag.name}
                  </Badge>
                ))}
              </div>
            ) : candidate.tags && candidate.tags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {candidate.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            ) : null}
          </CardContent>
        </Card>

        {/* Skills */}
        {candidate.skills &&
          candidate.skills.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Skills</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {candidate.skills.map((skill, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center"
                    >
                      <span className="font-medium">{skill.name}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{skill.level}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {skill.years}y
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

        {/* AI Summary */}
        {candidate.aiSummary && (
          <Card>
            <CardHeader>
              <CardTitle>AI Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm leading-relaxed">{candidate.aiSummary}</p>
            </CardContent>
          </Card>
        )}

        {/* Matched Jobs */}
        <MatchedJobsList />
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Relationship Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Relationship Score</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowRelationshipDialog(true)}
              >
                View Details
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {candidate.relationshipScore}%
              </div>
              <p className="text-sm text-muted-foreground">Match confidence</p>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <QuickActions candidateId={candidate.id} />
          </CardContent>
        </Card>

        {/* Recruiter Info */}
        {candidate.recruiter && candidate.recruiter.name && (
          <Card>
            <CardHeader>
              <CardTitle>Assigned Recruiter</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage
                    src={candidate.recruiter.avatar}
                    alt={candidate.recruiter.name}
                  />
                  <AvatarFallback>
                    {candidate.recruiter.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{candidate.recruiter.name}</p>
                  <p className="text-sm text-muted-foreground">Recruiter</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <RelationshipScoreDialog
        candidate={candidate}
        open={showRelationshipDialog}
        onOpenChange={setShowRelationshipDialog}
      />
    </div>
  );
}
