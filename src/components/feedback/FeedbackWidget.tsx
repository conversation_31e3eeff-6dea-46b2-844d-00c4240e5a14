import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import {
  MessageSquare,
  Bug,
  Lightbulb,
  Heart,
  AlertCircle,
  Send,
  X,
  Star,
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface FeedbackWidgetProps {
  featureName?: string;
  onClose?: () => void;
}

type FeedbackType = "bug" | "feature" | "improvement" | "praise";

export function FeedbackWidget({
  featureName = "general",
  onClose,
}: FeedbackWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<FeedbackType>("improvement");
  const [rating, setRating] = useState<number>(0);
  const [message, setMessage] = useState("");
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!message.trim()) {
      toast.error("Please provide feedback message");
      return;
    }

    setSubmitting(true);
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Please login to submit feedback");
        return;
      }

      const { error } = await supabase.from("user_feedback").insert({
        user_id: user.id,
        feature_name: featureName,
        feedback_type: feedbackType,
        rating: rating || null,
        message: message.trim(),
        metadata: {
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        },
      });

      if (error) throw error;

      toast.success("Thank you for your feedback!");

      // Reset form
      setMessage("");
      setRating(0);
      setFeedbackType("improvement");
      setIsOpen(false);

      if (onClose) onClose();
    } catch (error) {
      console.error("Error submitting feedback:", error);
      toast.error("Failed to submit feedback");
    } finally {
      setSubmitting(false);
    }
  };

  const feedbackIcons = {
    bug: <Bug className="h-5 w-5" />,
    feature: <Lightbulb className="h-5 w-5" />,
    improvement: <AlertCircle className="h-5 w-5" />,
    praise: <Heart className="h-5 w-5" />,
  };

  const feedbackLabels = {
    bug: "Report a Bug",
    feature: "Request Feature",
    improvement: "Suggest Improvement",
    praise: "Share Praise",
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90"
        >
          <MessageSquare className="h-6 w-6" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Share Your Feedback
          </DialogTitle>
          <DialogDescription>
            Help us improve by sharing your thoughts
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Feedback Type Selection */}
          <div className="space-y-3">
            <Label>What type of feedback do you have?</Label>
            <RadioGroup
              value={feedbackType}
              onValueChange={(value) => setFeedbackType(value as FeedbackType)}
            >
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(feedbackLabels).map(([type, label]) => (
                  <div key={type} className="flex items-center space-x-2">
                    <RadioGroupItem value={type} id={type} />
                    <Label
                      htmlFor={type}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      {feedbackIcons[type as FeedbackType]}
                      {label}
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>

          {/* Rating (optional) */}
          <div className="space-y-3">
            <Label>How would you rate your experience? (optional)</Label>
            <div className="flex gap-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  className="p-1 hover:scale-110 transition-transform"
                >
                  <Star
                    className={`h-6 w-6 ${
                      star <= rating
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-gray-300"
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Feedback Message */}
          <div className="space-y-3">
            <Label htmlFor="feedback-message">Your feedback</Label>
            <Textarea
              id="feedback-message"
              placeholder={
                feedbackType === "bug"
                  ? "Describe the issue you encountered..."
                  : feedbackType === "feature"
                    ? "What feature would you like to see?"
                    : feedbackType === "improvement"
                      ? "How can we improve this?"
                      : "What did you like?"
              }
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsOpen(false);
                if (onClose) onClose();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !message.trim()}
            >
              {submitting ? (
                "Sending..."
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Feedback
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Feature Request Widget
export function FeatureRequestWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [requestType, setRequestType] = useState<
    "node" | "integration" | "feature"
  >("feature");
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [useCase, setUseCase] = useState("");
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!name.trim() || !description.trim()) {
      toast.error("Please provide name and description");
      return;
    }

    setSubmitting(true);
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Please login to submit request");
        return;
      }

      const { error } = await supabase.from("feature_requests").insert({
        user_id: user.id,
        request_type: requestType,
        name: name.trim(),
        description: description.trim(),
        use_case: useCase.trim() || null,
        metadata: {
          submitted_from: window.location.href,
          timestamp: new Date().toISOString(),
        },
      });

      if (error) throw error;

      toast.success("Feature request submitted successfully!");

      // Reset form
      setName("");
      setDescription("");
      setUseCase("");
      setIsOpen(false);
    } catch (error) {
      console.error("Error submitting request:", error);
      toast.error("Failed to submit request");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Lightbulb className="h-4 w-4 mr-2" />
          Request Feature
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Request a Feature</DialogTitle>
          <DialogDescription>
            Tell us what you'd like to see in the platform
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Request Type */}
          <div className="space-y-2">
            <Label>What would you like to request?</Label>
            <RadioGroup
              value={requestType}
              onValueChange={(value) =>
                setRequestType(value as typeof requestType)
              }
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="node" id="node" />
                <Label htmlFor="node">New Workflow Node</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="integration" id="integration" />
                <Label htmlFor="integration">New Integration</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="feature" id="feature" />
                <Label htmlFor="feature">Platform Feature</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="request-name">
              {requestType === "node"
                ? "Node Name"
                : requestType === "integration"
                  ? "Integration Name"
                  : "Feature Name"}
            </Label>
            <input
              id="request-name"
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              placeholder={
                requestType === "node"
                  ? "e.g., Data Validation Node"
                  : requestType === "integration"
                    ? "e.g., Salesforce Integration"
                    : "e.g., Dark Mode Support"
              }
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="request-description">Description</Label>
            <Textarea
              id="request-description"
              placeholder="Describe what this should do and why it's needed..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          {/* Use Case */}
          <div className="space-y-2">
            <Label htmlFor="request-usecase">Use Case (optional)</Label>
            <Textarea
              id="request-usecase"
              placeholder="Describe how you would use this..."
              value={useCase}
              onChange={(e) => setUseCase(e.target.value)}
              rows={2}
            />
          </div>

          {/* Submit */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !name.trim() || !description.trim()}
            >
              {submitting ? "Submitting..." : "Submit Request"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
