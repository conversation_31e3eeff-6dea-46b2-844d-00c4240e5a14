import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  FileText,
  Download,
  Calendar,
  BarChart3,
  Users,
  Briefcase,
  TrendingUp,
  PieChart,
  Activity,
  Filter,
  Settings,
  RefreshCw,
  Eye,
  Send,
  <PERSON><PERSON><PERSON>,
  <PERSON>ader2,
} from "lucide-react";
import { generateText } from "@/utils/gemini";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import {
  useReportTemplates,
  useGeneratedReports,
  useCreateGeneratedReport,
  useInitializeReportGenerator,
} from "@/hooks/useReportGenerator";
import {
  useDueScheduledReports,
  useMyReportHistory,
  useReportGenerationQueue,
} from "@/hooks/useReports";
import { useAuth } from "@/contexts/AuthContext";

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: "candidate" | "job" | "analytics" | "performance";
  format: "pdf" | "excel" | "csv";
  metrics: string[];
  lastGenerated?: string;
}

interface GeneratedReport {
  id: string;
  name: string;
  type: string;
  format: string;
  generatedAt: string;
  size: string;
  downloadUrl: string;
}

export function ReportGenerator() {
  const { user } = useAuth();
  const userId = user?.id;
  const { toast } = useToast();

  const {
    data: reportTemplatesData = [],
    isLoading: templatesLoading,
    error: templatesError,
  } = useReportTemplates(userId || "");

  const {
    data: generatedReportsData = [],
    isLoading: reportsLoading,
    error: reportsError,
  } = useGeneratedReports(userId || "");

  const { initializeAllData, isInitializing } = useInitializeReportGenerator();

  const createGeneratedReport = useCreateGeneratedReport();

  const { data: dueScheduledReports = [], isLoading: dueLoading } =
    useDueScheduledReports();
  const { data: myReportHistory = [], isLoading: historyLoading } =
    useMyReportHistory();
  const { data: reportQueue = [], isLoading: queueLoading } =
    useReportGenerationQueue();

  const [selectedTemplate, setSelectedTemplate] =
    useState<ReportTemplate | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [dateRange, setDateRange] = useState({
    from: "",
    to: "",
  });
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [aiInsights, setAiInsights] = useState<string>("");
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false);

  // Initialize data if empty
  useEffect(() => {
    if (
      userId &&
      reportTemplatesData.length === 0 &&
      generatedReportsData.length === 0 &&
      !templatesLoading &&
      !reportsLoading &&
      !templatesError &&
      !reportsError
    ) {
      initializeAllData(userId);
    }
  }, [
    userId,
    reportTemplatesData.length,
    generatedReportsData.length,
    templatesLoading,
    reportsLoading,
    templatesError,
    reportsError,
    initializeAllData,
  ]);

  // Transform database data to component format
  const reportTemplates: ReportTemplate[] = reportTemplatesData.map(
    (template) => ({
      id: template.id,
      name: template.name,
      description: template.description || "",
      type: template.type,
      format: template.format,
      metrics: template.metrics,
      lastGenerated: template.last_generated || undefined,
    }),
  );

  const generatedReports: GeneratedReport[] = generatedReportsData.map(
    (report) => ({
      id: report.id,
      name: report.name,
      type: report.type,
      format: report.format,
      generatedAt: report.generated_at,
      size: report.file_size,
      downloadUrl: report.download_url,
    }),
  );

  const availableMetrics = [
    "Total Candidates",
    "Source Breakdown",
    "Status Distribution",
    "Skills Analysis",
    "Time to Hire",
    "Cost per Hire",
    "Source Effectiveness",
    "Conversion Rates",
    "Job Views",
    "Application Rates",
    "Time to Fill",
    "Salary Benchmarks",
    "Stage Conversion",
    "Bottleneck Analysis",
    "Interview Success",
    "Offer Acceptance",
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "candidate":
        return Users;
      case "job":
        return Briefcase;
      case "analytics":
        return BarChart3;
      case "performance":
        return TrendingUp;
      default:
        return FileText;
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case "candidate":
        return "bg-blue-100 text-blue-800";
      case "job":
        return "bg-green-100 text-green-800";
      case "analytics":
        return "bg-purple-100 text-purple-800";
      case "performance":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleGenerateReport = async () => {
    if (!selectedTemplate || !userId) return;

    setIsGenerating(true);
    try {
      // Create a new generated report record
      const reportData = {
        id: `${selectedTemplate.id}-${Date.now()}`,
        user_id: userId,
        name: `${selectedTemplate.name} - ${new Date().toLocaleDateString()}`,
        type: selectedTemplate.type,
        format: selectedTemplate.format.toUpperCase(),
        file_size: `${(Math.random() * 3 + 1).toFixed(1)} MB`,
        download_url: "#",
      };

      await createGeneratedReport.mutateAsync(reportData);

      // Simulate report generation delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast({
        title: "Report Generated",
        description: `${selectedTemplate.name} has been generated successfully.`,
      });

      setSelectedTemplate(null);
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate report. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadReport = (report: GeneratedReport) => {
    toast({
      title: "Download Started",
      description: `Downloading ${report.name}...`,
    });
  };

  const handleScheduleReport = (template: ReportTemplate) => {
    toast({
      title: "Report Scheduled",
      description: `${template.name} has been scheduled for automated generation.`,
    });
  };

  const generateAIInsights = async () => {
    if (isGeneratingInsights || !selectedTemplate) return;

    setIsGeneratingInsights(true);
    try {
      const prompt = `
Generate actionable insights for a recruitment report with the following details:

Report Type: ${selectedTemplate.name}
Description: ${selectedTemplate.description}
Metrics Included: ${selectedMetrics.join(", ")}
Date Range: ${dateRange.from || "Not specified"} to ${dateRange.to || "Not specified"}

Provide insights that would be valuable for recruitment teams, including:
1. Key trends and patterns to look for
2. Actionable recommendations based on typical data patterns
3. Important metrics to monitor
4. Strategic suggestions for improvement

Keep the response concise (2-3 paragraphs) and focused on practical value for recruiters and hiring managers.
`;

      const systemPrompt =
        "You are an expert recruitment analytics consultant. Provide strategic insights that help recruitment teams make data-driven decisions and improve their hiring processes.";

      const insights = await generateText(prompt, systemPrompt);
      setAiInsights(insights);

      toast({
        title: "AI Insights Generated",
        description: "Strategic insights for your report are ready.",
      });
    } catch (error) {
      console.error("Error generating AI insights:", error);
      toast({
        title: "Insights Unavailable",
        description: "Unable to generate AI insights at the moment.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingInsights(false);
    }
  };

  const isLoading = templatesLoading || reportsLoading || isInitializing;
  const hasError = templatesError || reportsError;

  if (isLoading) {
    return (
      <div className="space-y-6 px-4 sm:px-0">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-9 w-32" />
        </div>

        <Tabs defaultValue="templates" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 h-auto">
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="generated">Generated</TabsTrigger>
            <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader className="pb-3">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-16" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-20 w-full" />
                    <div className="flex gap-2">
                      <Skeleton className="h-9 flex-1" />
                      <Skeleton className="h-9 w-16" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  if (hasError) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          Failed to load report generator data:{" "}
          {(templatesError || reportsError)?.message}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6 px-4 sm:px-0">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold tracking-tight">
            Report Generator
          </h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            Generate comprehensive reports for your recruitment analytics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            <RefreshCw className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Refresh Data</span>
            <span className="sm:hidden">Refresh</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="templates" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 h-auto">
          <TabsTrigger value="templates" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Report Templates</span>
            <span className="sm:hidden">Templates</span>
          </TabsTrigger>
          <TabsTrigger value="generated" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Generated Reports</span>
            <span className="sm:hidden">Generated</span>
          </TabsTrigger>
          <TabsTrigger value="scheduled" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Scheduled Reports</span>
            <span className="sm:hidden">Scheduled</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {reportTemplates.map((template) => {
              const TypeIcon = getTypeIcon(template.type);

              return (
                <Card
                  key={template.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div className="p-2 rounded-lg bg-primary/10 flex-shrink-0">
                          <TypeIcon className="h-5 w-5 text-primary" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <CardTitle className="text-base sm:text-lg truncate">
                            {template.name}
                          </CardTitle>
                          <Badge
                            className={getTypeBadgeColor(template.type)}
                            variant="outline"
                          >
                            {template.type}
                          </Badge>
                        </div>
                      </div>
                      <Badge
                        variant="outline"
                        className="self-start sm:self-auto"
                      >
                        {template.format.toUpperCase()}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      {template.description}
                    </p>

                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">
                        Included Metrics
                      </Label>
                      <div className="flex flex-wrap gap-1">
                        {template.metrics.slice(0, 2).map((metric) => (
                          <Badge
                            key={metric}
                            variant="secondary"
                            className="text-xs"
                          >
                            <span className="truncate max-w-[120px] sm:max-w-none">
                              {metric}
                            </span>
                          </Badge>
                        ))}
                        {template.metrics.length > 2 && (
                          <Badge variant="secondary" className="text-xs">
                            +{template.metrics.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    {template.lastGenerated && (
                      <div className="text-xs text-muted-foreground">
                        Last generated:{" "}
                        {new Date(template.lastGenerated).toLocaleDateString()}
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            className="flex-1 w-full"
                            onClick={() => {
                              setSelectedTemplate(template);
                              setSelectedMetrics(template.metrics);
                            }}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            Generate
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                              <TypeIcon className="h-5 w-5" />
                              Generate {selectedTemplate?.name}
                            </DialogTitle>
                            <DialogDescription>
                              Configure your report settings before generation.
                            </DialogDescription>
                          </DialogHeader>

                          {selectedTemplate && (
                            <div className="space-y-6">
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                  <Label htmlFor="date-from">From Date</Label>
                                  <Input
                                    id="date-from"
                                    type="date"
                                    value={dateRange.from}
                                    onChange={(e) =>
                                      setDateRange((prev) => ({
                                        ...prev,
                                        from: e.target.value,
                                      }))
                                    }
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="date-to">To Date</Label>
                                  <Input
                                    id="date-to"
                                    type="date"
                                    value={dateRange.to}
                                    onChange={(e) =>
                                      setDateRange((prev) => ({
                                        ...prev,
                                        to: e.target.value,
                                      }))
                                    }
                                  />
                                </div>
                              </div>

                              <div className="space-y-3">
                                <Label>Select Metrics to Include</Label>
                                <ScrollArea className="h-32 border rounded-lg p-3">
                                  <div className="space-y-2">
                                    {availableMetrics.map((metric) => (
                                      <div
                                        key={metric}
                                        className="flex items-center space-x-2"
                                      >
                                        <Checkbox
                                          id={metric}
                                          checked={selectedMetrics.includes(
                                            metric,
                                          )}
                                          onCheckedChange={(checked) => {
                                            if (checked) {
                                              setSelectedMetrics((prev) => [
                                                ...prev,
                                                metric,
                                              ]);
                                            } else {
                                              setSelectedMetrics((prev) =>
                                                prev.filter(
                                                  (m) => m !== metric,
                                                ),
                                              );
                                            }
                                          }}
                                        />
                                        <Label
                                          htmlFor={metric}
                                          className="text-sm break-words"
                                        >
                                          {metric}
                                        </Label>
                                      </div>
                                    ))}
                                  </div>
                                </ScrollArea>
                              </div>

                              {/* AI Insights Section */}
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <Label className="flex items-center gap-2">
                                    <Sparkles className="h-4 w-4" />
                                    AI Report Insights
                                  </Label>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={generateAIInsights}
                                    disabled={
                                      isGeneratingInsights ||
                                      !selectedMetrics.length
                                    }
                                  >
                                    {isGeneratingInsights ? (
                                      <>
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                        Analyzing...
                                      </>
                                    ) : (
                                      <>
                                        <Sparkles className="h-4 w-4 mr-2" />
                                        Generate Insights
                                      </>
                                    )}
                                  </Button>
                                </div>

                                {aiInsights && (
                                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                                    <div className="text-sm text-gray-700 whitespace-pre-wrap">
                                      {aiInsights}
                                    </div>
                                  </div>
                                )}
                              </div>

                              <div className="flex flex-col sm:flex-row gap-2">
                                <Button
                                  onClick={handleGenerateReport}
                                  disabled={isGenerating}
                                  className="flex-1 w-full"
                                >
                                  {isGenerating ? (
                                    <>
                                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                      Generating...
                                    </>
                                  ) : (
                                    <>
                                      <Download className="h-4 w-4 mr-2" />
                                      Generate Report
                                    </>
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  onClick={() => setSelectedTemplate(null)}
                                  className="w-full sm:w-auto"
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleScheduleReport(template)}
                        className="w-full sm:w-auto"
                      >
                        <Calendar className="h-4 w-4 sm:mr-0 mr-2" />
                        <span className="sm:hidden">Schedule</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="generated" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Generated Reports</CardTitle>
            </CardHeader>
            <CardContent>
              {generatedReports.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Reports Generated
                  </h3>
                  <p className="text-muted-foreground">
                    Generate your first report to see it here.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {generatedReports.map((report) => (
                    <div
                      key={report.id}
                      className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-lg gap-4"
                    >
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <FileText className="h-8 w-8 text-primary flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium truncate">
                            {report.name}
                          </h4>
                          <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
                            <span>{report.type}</span>
                            <span>{report.format}</span>
                            <span>{report.size}</span>
                            <span className="hidden sm:inline">
                              {new Date(
                                report.generatedAt,
                              ).toLocaleDateString()}
                            </span>
                            <span className="sm:hidden">
                              {new Date(report.generatedAt).toLocaleDateString(
                                "en-US",
                                { month: "short", day: "numeric" },
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full sm:w-auto"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          <span className="hidden sm:inline">Preview</span>
                          <span className="sm:hidden">Preview</span>
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleDownloadReport(report)}
                          className="w-full sm:w-auto"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  No Scheduled Reports
                </h3>
                <p className="text-muted-foreground">
                  Schedule reports for automatic generation at regular
                  intervals.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Tabs defaultValue="reports" className="w-full mt-6">
        <TabsList>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="due">Due Scheduled</TabsTrigger>
          <TabsTrigger value="history">My History</TabsTrigger>
          <TabsTrigger value="queue">Queue</TabsTrigger>
        </TabsList>
        <TabsContent value="reports">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            {reportTemplates.map((template) => {
              const TypeIcon = getTypeIcon(template.type);

              return (
                <Card
                  key={template.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div className="p-2 rounded-lg bg-primary/10 flex-shrink-0">
                          <TypeIcon className="h-5 w-5 text-primary" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <CardTitle className="text-base sm:text-lg truncate">
                            {template.name}
                          </CardTitle>
                          <Badge
                            className={getTypeBadgeColor(template.type)}
                            variant="outline"
                          >
                            {template.type}
                          </Badge>
                        </div>
                      </div>
                      <Badge
                        variant="outline"
                        className="self-start sm:self-auto"
                      >
                        {template.format.toUpperCase()}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      {template.description}
                    </p>

                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">
                        Included Metrics
                      </Label>
                      <div className="flex flex-wrap gap-1">
                        {template.metrics.slice(0, 2).map((metric) => (
                          <Badge
                            key={metric}
                            variant="secondary"
                            className="text-xs"
                          >
                            <span className="truncate max-w-[120px] sm:max-w-none">
                              {metric}
                            </span>
                          </Badge>
                        ))}
                        {template.metrics.length > 2 && (
                          <Badge variant="secondary" className="text-xs">
                            +{template.metrics.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    {template.lastGenerated && (
                      <div className="text-xs text-muted-foreground">
                        Last generated:{" "}
                        {new Date(template.lastGenerated).toLocaleDateString()}
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            className="flex-1 w-full"
                            onClick={() => {
                              setSelectedTemplate(template);
                              setSelectedMetrics(template.metrics);
                            }}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            Generate
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                              <TypeIcon className="h-5 w-5" />
                              Generate {selectedTemplate?.name}
                            </DialogTitle>
                            <DialogDescription>
                              Configure your report settings before generation.
                            </DialogDescription>
                          </DialogHeader>

                          {selectedTemplate && (
                            <div className="space-y-6">
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                  <Label htmlFor="date-from">From Date</Label>
                                  <Input
                                    id="date-from"
                                    type="date"
                                    value={dateRange.from}
                                    onChange={(e) =>
                                      setDateRange((prev) => ({
                                        ...prev,
                                        from: e.target.value,
                                      }))
                                    }
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="date-to">To Date</Label>
                                  <Input
                                    id="date-to"
                                    type="date"
                                    value={dateRange.to}
                                    onChange={(e) =>
                                      setDateRange((prev) => ({
                                        ...prev,
                                        to: e.target.value,
                                      }))
                                    }
                                  />
                                </div>
                              </div>

                              <div className="space-y-3">
                                <Label>Select Metrics to Include</Label>
                                <ScrollArea className="h-32 border rounded-lg p-3">
                                  <div className="space-y-2">
                                    {availableMetrics.map((metric) => (
                                      <div
                                        key={metric}
                                        className="flex items-center space-x-2"
                                      >
                                        <Checkbox
                                          id={metric}
                                          checked={selectedMetrics.includes(
                                            metric,
                                          )}
                                          onCheckedChange={(checked) => {
                                            if (checked) {
                                              setSelectedMetrics((prev) => [
                                                ...prev,
                                                metric,
                                              ]);
                                            } else {
                                              setSelectedMetrics((prev) =>
                                                prev.filter(
                                                  (m) => m !== metric,
                                                ),
                                              );
                                            }
                                          }}
                                        />
                                        <Label
                                          htmlFor={metric}
                                          className="text-sm break-words"
                                        >
                                          {metric}
                                        </Label>
                                      </div>
                                    ))}
                                  </div>
                                </ScrollArea>
                              </div>

                              <div className="flex flex-col sm:flex-row gap-2">
                                <Button
                                  onClick={handleGenerateReport}
                                  disabled={isGenerating}
                                  className="flex-1 w-full"
                                >
                                  {isGenerating ? (
                                    <>
                                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                      Generating...
                                    </>
                                  ) : (
                                    <>
                                      <Download className="h-4 w-4 mr-2" />
                                      Generate Report
                                    </>
                                  )}
                                </Button>
                                <Button
                                  variant="outline"
                                  onClick={() => setSelectedTemplate(null)}
                                  className="w-full sm:w-auto"
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleScheduleReport(template)}
                        className="w-full sm:w-auto"
                      >
                        <Calendar className="h-4 w-4 sm:mr-0 mr-2" />
                        <span className="sm:hidden">Schedule</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
        <TabsContent value="due">
          <h3 className="text-lg font-semibold mb-2">Due Scheduled Reports</h3>
          {dueLoading ? (
            <div>Loading...</div>
          ) : (
            <ul>
              {dueScheduledReports.map((r: any) => (
                <li key={r.id}>
                  {r.name} (Next run: {r.next_run_at})
                </li>
              ))}
            </ul>
          )}
        </TabsContent>
        <TabsContent value="history">
          <h3 className="text-lg font-semibold mb-2">My Report History</h3>
          {historyLoading ? (
            <div>Loading...</div>
          ) : (
            <ul>
              {myReportHistory.map((h: any) => (
                <li key={h.id}>
                  {h.name} ({h.status})
                </li>
              ))}
            </ul>
          )}
        </TabsContent>
        <TabsContent value="queue">
          <h3 className="text-lg font-semibold mb-2">
            Report Generation Queue
          </h3>
          {queueLoading ? (
            <div>Loading...</div>
          ) : (
            <ul>
              {reportQueue.map((q: any) => (
                <li key={q.id}>
                  {q.name} ({q.status})
                </li>
              ))}
            </ul>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
